using Weso.ProcessTorreKlabin.Business;
using Weso.ProcessTorreKlabin.Repository;

namespace Weso.ProcessTorreKlabin.Workers
{
    public class TorreKlabinWorker(ILogger<TorreKlabinWorker> logger, PositionHandler positionHandler, IPositionRepository positionRepository) : BackgroundService
    {
        private async Task ProcessPositions(CancellationToken cancellationToken)
        {
            var positions = await positionRepository.GetNextAsync(5000).ConfigureAwait(false);

            if (positions.Count > 0)
            {
                var orderedPositions = positions.OrderBy(x => x.Dt).ToList();

                await positionHandler.Process(orderedPositions, "WESO");

                _logger.LogInformation("Processadas {Count} posições e enviadas via SignalR", positions.Count);

                var positionIds = positions.Select(x => x.PosId).ToList();
                await positionRepository.RemoveRangeAsync(positionIds).ConfigureAwait(false);

                await Task.Delay(1000, cancellationToken).ConfigureAwait(false);
            }
            else
            {
                await Task.Delay(5000, cancellationToken).ConfigureAwait(false);
            }
        }

        private readonly ILogger<TorreKlabinWorker> _logger = logger;

        protected override Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("Torre Klabin Worker iniciado");

            return Task.Run(async () =>
            {
                while (!stoppingToken.IsCancellationRequested)
                {
                    try
                    {
                        await ProcessPositions(stoppingToken);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Erro ao processar posições");
                        await Task.Delay(5000, stoppingToken);
                    }
                }
            }, stoppingToken);
        }
    }
}