using System.ComponentModel.DataAnnotations;

namespace Weso.ProcessTorreKlabin.Dto;

/// <summary>
/// DTO para requisição de login
/// </summary>
public class LoginDto
{
    /// <summary>
    /// E-mail do usuário
    /// </summary>
    /// <example><EMAIL></example>
    [Required(ErrorMessage = "Login é obrigatório")]
    [EmailAddress(ErrorMessage = "Formato de e-mail inválido")]
    public string Login { get; set; }

    /// <summary>
    /// Senha do usuário
    /// </summary>
    /// <example>Teste@Klabin</example>
    [Required(ErrorMessage = "Senha é obrigatória")]
    [MinLength(6, ErrorMessage = "Senha deve ter pelo menos 6 caracteres")]
    public string Senha { get; set; }
}

/// <summary>
/// DTO para resposta de login
/// </summary>
public class LoginResponseDto
{
    /// <summary>
    /// Token JWT para autenticação
    /// </summary>
    public string Token { get; set; }

    /// <summary>
    /// ID do cliente
    /// </summary>
    public int CliId { get; set; }

    /// <summary>
    /// Login do usuário
    /// </summary>
    public string Login { get; set; }
}