using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using Weso.ProcessTorreKlabin.Business;
using Weso.ProcessTorreKlabin.Dto;
using Weso.ProcessTorreKlabin.Hubs;
using Weso.ProcessTorreKlabin.Repository;
using Weso.ProcessTorreKlabin.Utils;

namespace Weso.ProcessTorreKlabin.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class VehicleController(ICacheRepository cacheRepository, IPlacaRepository placaRepository, PlacaCacheService placaCacheService, IHubContext<VehicleHub> hubContext) : ControllerBase
{
    private readonly ICacheRepository _cacheRepository = cacheRepository;
    private readonly IHubContext<VehicleHub> _hubContext = hubContext;
    private readonly PlacaCacheService _placaCacheService = placaCacheService;
    private readonly IPlacaRepository _placaRepository = placaRepository;

    [HttpPost("placa")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(401)]
    public async Task<IActionResult> CadastrarPlaca([FromBody] string placa)
    {
        if (string.IsNullOrWhiteSpace(placa))
        {
            return BadRequest("Placa não pode ser vazia");
        }

        var placaNormalizada = VehicleHelper.GetPlacaPrefix(placa);

        if (string.IsNullOrEmpty(placaNormalizada))
        {
            return BadRequest("Placa inválida após normalização");
        }

        var existingPlaca = await _placaRepository.GetByPlacaAsync(placaNormalizada);
        if (existingPlaca != null)
        {
            return BadRequest("Placa já cadastrada");
        }

        var cliId = User.FindFirst("CliId")?.Value ?? "0";
        var novaPlaca = new PlacaDto
        {
            Cli = cliId,
            Placa = placaNormalizada
        };

        await _placaRepository.CreateAsync(novaPlaca);

        await _placaCacheService.AddPlacaAsync(placaNormalizada, cliId);

        return Ok(new { message = "Placa cadastrada com sucesso", placa = placaNormalizada });
    }

    [HttpDelete("placa/{placa}")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(401)]
    public async Task<IActionResult> ExcluirPlaca(string placa)
    {
        if (string.IsNullOrWhiteSpace(placa))
        {
            return BadRequest("Placa não pode ser vazia");
        }

        var placaNormalizada = VehicleHelper.GetPlacaPrefix(placa);
        var cliId = User.FindFirst("CliId")?.Value;

        var existingPlaca = await _placaRepository.GetByPlacaAsync(placaNormalizada);
        if (existingPlaca == null || existingPlaca.Cli != cliId)
        {
            return BadRequest("Placa não encontrada ou não pertence ao cliente");
        }

        var deleted = await _placaRepository.DeleteByPlacaAsync(placaNormalizada);
        if (deleted)
        {
            await _placaCacheService.RemovePlacaAsync(placaNormalizada);
            return Ok(new { message = "Placa excluída com sucesso", placa = placaNormalizada });
        }

        return BadRequest("Erro ao excluir placa");
    }

    [HttpGet]
    [ProducesResponseType(typeof(List<CacheEqpDto>), 200)]
    [ProducesResponseType(401)]
    public IActionResult GetAllVehicles(string integration = "WESO")
    {
        var cliId = User.FindFirst("CliId")?.Value;
        var vehicles = _cacheRepository.GetAllEquipments(cliId, integration);
        return Ok(vehicles);
    }

    [HttpGet("placas")]
    [ProducesResponseType(typeof(List<string>), 200)]
    [ProducesResponseType(401)]
    public async Task<IActionResult> GetPlacas()
    {
        var cliId = User.FindFirst("CliId")?.Value;
        var placasDto = await _placaRepository.GetByCliAsync(cliId);
        var placas = placasDto.Select(p => p.Placa).ToList();
        return Ok(placas);
    }

    [HttpPost("notify-update")]
    [ProducesResponseType(200)]
    [ProducesResponseType(401)]
    public async Task<IActionResult> NotifyUpdate([FromBody] object data)
    {
        var cliId = User.FindFirst("CliId")?.Value;
        await _hubContext.Clients.Group($"cli_{cliId}").SendAsync("VehicleUpdate", data);
        return Ok();
    }

    [HttpPost("set-route")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(401)]
    public IActionResult SetVehicleRoute([FromBody] SetVehicleRouteDto request)
    {
        if (string.IsNullOrWhiteSpace(request.Placa))
        {
            return BadRequest("Placa não pode ser vazia");
        }

        if (string.IsNullOrWhiteSpace(request.OrigemPcoId))
        {
            return BadRequest("PCO de origem é obrigatório");
        }

        if (string.IsNullOrWhiteSpace(request.DestinoPcoId))
        {
            return BadRequest("PCO de destino é obrigatório");
        }

        if (request.DataLimite.Date <= DateTime.Now.Date)
        {
            return BadRequest("Previsão de descarga deve ser futura");
        }

        var cliId = User.FindFirst("CliId")?.Value;
        var placaNormalizada = VehicleHelper.GetPlacaPrefix(request.Placa);

        var origemPco = _cacheRepository.GetPco(cliId, request.OrigemPcoId);
        if (origemPco == null)
        {
            return BadRequest("PCO de origem não encontrado");
        }

        if (origemPco.Tipo != "C")
        {
            return BadRequest("PCO de origem deve ser do tipo Carga (C)");
        }

        var destinoPco = _cacheRepository.GetPco(cliId, request.DestinoPcoId);
        if (destinoPco == null)
        {
            return BadRequest("PCO de destino não encontrado");
        }

        if (destinoPco.Tipo != "D")
        {
            return BadRequest("PCO de destino deve ser do tipo Descarga (D)");
        }

        var currentCache = _cacheRepository.GetCachePlaca(cliId, placaNormalizada, "WESO");
        if (currentCache == null)
        {
            return BadRequest("Veículo não encontrado no sistema");
        }

        currentCache.OrigemPcoId = request.OrigemPcoId;
        currentCache.DestinoPcoId = request.DestinoPcoId;
        currentCache.DataLimite = request.DataLimite;

        _cacheRepository.SetCacheEqp(cliId, placaNormalizada, currentCache, "WESO");

        return Ok(new { message = "Rota configurada com sucesso", placa = placaNormalizada });
    }

    [HttpDelete("remove-route/{placa}")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(401)]
    public IActionResult RemoveVehicleRoute(string placa)
    {
        if (string.IsNullOrWhiteSpace(placa))
        {
            return BadRequest("Placa não pode ser vazia");
        }

        var cliId = User.FindFirst("CliId")?.Value;
        var placaNormalizada = VehicleHelper.GetPlacaPrefix(placa);

        var currentCache = _cacheRepository.GetCachePlaca(cliId, placaNormalizada, "WESO");
        if (currentCache == null)
        {
            return BadRequest("Veículo não encontrado no sistema");
        }

        currentCache.OrigemPcoId = "";
        currentCache.DestinoPcoId = "";
        currentCache.DataLimite = null;

        _cacheRepository.SetCacheEqp(cliId, placaNormalizada, currentCache, "WESO");

        return Ok(new { message = "Rota removida com sucesso", placa = placaNormalizada });
    }
}