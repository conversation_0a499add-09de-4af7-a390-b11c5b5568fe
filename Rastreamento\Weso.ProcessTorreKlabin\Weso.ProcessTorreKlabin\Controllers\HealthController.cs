using Microsoft.AspNetCore.Mvc;

namespace Weso.ProcessTorreKlabin.Controllers;

[ApiController]
[Route("api/[controller]")]
public class HealthController(ILogger<HealthController> logger) : ControllerBase
{
    private readonly ILogger<HealthController> _logger = logger;

    [HttpGet]
    [ProducesResponseType(typeof(object), 200)]
    public IActionResult Get()
    {
        try
        {
            return Ok(new
            {
                Status = "Healthy",
                Timestamp = DateTime.Now,
                Service = "Weso.ProcessTorreKlabin",
                Version = "1.0.0"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Health check failed");

            return StatusCode(500, new
            {
                Status = "Unhealthy",
                Timestamp = DateTime.Now,
                Error = ex.Message
            });
        }
    }
}