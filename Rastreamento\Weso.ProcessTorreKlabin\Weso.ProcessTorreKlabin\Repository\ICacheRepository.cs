using Weso.ProcessTorreKlabin.Dto;

namespace Weso.ProcessTorreKlabin.Repository;

public interface ICacheRepository
{
    bool DeletePco(string cli, string id);

    List<CacheEqpDto> GetAllEquipments(string cli, string integracao);

    CacheEqpDto? GetCachePlaca(string cli, string placa, string integracao);

    PcoDto? GetPco(string cli, string id);

    List<PcoDto> GetPcos(string cli);

    void SetCacheEqp(string cli, string placa, CacheEqpDto cacheEqp, string integracao);

    void SetPco(string cli, PcoDto pco);

    void SetPcos(string cli, List<PcoDto> pcos);
}