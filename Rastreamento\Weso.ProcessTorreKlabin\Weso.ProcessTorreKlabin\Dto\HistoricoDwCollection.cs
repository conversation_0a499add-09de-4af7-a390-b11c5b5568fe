﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace Weso.ProcessTorreKlabin.Dto;

[BsonIgnoreExtraElements]
public class HistoricoDwCollection
{
    public int Cli { get; set; }

    public DateTime Dt { get; set; }

    public string DtF { get; set; }

    public int Eqp { get; set; }

    [BsonId]
    public ObjectId Id { get; set; }

    public int Ign { get; set; }
    public double Lat { get; set; }
    public double Lon { get; set; }
    public int Mot { get; set; }
    public float Odom { get; set; }
    public string Placa { get; set; }
    public int Vel { get; set; }
}