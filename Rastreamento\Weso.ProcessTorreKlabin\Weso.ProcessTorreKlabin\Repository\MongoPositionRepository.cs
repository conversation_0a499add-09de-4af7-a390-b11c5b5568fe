using MongoDB.Bson;
using MongoDB.Driver;
using Weso.ProcessTorreKlabin.Dto;

namespace Weso.ProcessTorreKlabin.Repository;

public class MongoPositionRepository : IPositionRepository
{
    private readonly IMongoCollection<HistoricoDwCollection> _collection;

    public async Task<List<PositionDto>> GetNextAsync(int count)
    {
        var positions = await _collection
            .Find(FilterDefinition<HistoricoDwCollection>.Empty)
            .Limit(count)
            .ToListAsync();

        return positions.Select(x => new PositionDto
        {
            Placa = x.Placa,
            Dt = x.Dt,
            DtF = x.DtF,
            Eqp = x.Eqp,
            Lat = x.Lat,
            Lon = x.Lon,
            Mot = x.Mot,
            Odom = x.Odom,
            PosId = x.Id,
            Vel = x.Vel,
            Ign = (x.Ign & 1) == 1
        }).ToList();
    }

    public MongoPositionRepository(IConfiguration configuration)
    {
        var connectionString = configuration.GetConnectionString("MongoDB");
        var client = new MongoClient(connectionString);
        var database = client.GetDatabase("dwhist");
        _collection = database.GetCollection<HistoricoDwCollection>("processKlabin");
    }

    public async Task RemoveRangeAsync(List<ObjectId> positionIds)
    {
        var filter = Builders<HistoricoDwCollection>.Filter.In(x => x.Id, positionIds);
        await _collection.DeleteManyAsync(filter);
    }
}