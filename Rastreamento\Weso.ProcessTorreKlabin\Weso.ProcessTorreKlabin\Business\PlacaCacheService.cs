using Microsoft.Extensions.Caching.Memory;
using Weso.ProcessTorreKlabin.Repository;
using Weso.ProcessTorreKlabin.Utils;

namespace Weso.ProcessTorreKlabin.Business;

public class PlacaCacheService
{
    private async Task<Dictionary<string, string>> GetCacheAsync()
    {
        if (_memoryCache.TryGetValue(CACHE_KEY, out Dictionary<string, string>? cache) && cache != null)
        {
            return cache;
        }

        return await RefreshCacheAsync();
    }

    private async Task<Dictionary<string, string>> RefreshCacheAsync()
    {
        try
        {
            _logger.LogInformation("Recarregando cache de placas...");

            var todasPlacas = await _placaRepository.GetAllPlacasAsync();
            var cache = todasPlacas.ToDictionary(p => p.Placa, p => p.Cli);

            _memoryCache.Set(CACHE_KEY, cache, TimeSpan.FromMinutes(REFRESH_MINUTES + 5));

            _logger.LogInformation("Cache de placas recarregado com {Count} placas", cache.Count);
            return cache;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao recarregar cache de placas");
            return _memoryCache.TryGetValue(CACHE_KEY, out Dictionary<string, string>? existingCache) && existingCache != null
                ? existingCache
                : new Dictionary<string, string>();
        }
    }

    private const string CACHE_KEY = "placas_cache";
    private const int REFRESH_MINUTES = 10;
    private readonly ILogger<PlacaCacheService> _logger;
    private readonly IMemoryCache _memoryCache;
    private readonly IPlacaRepository _placaRepository;
    private readonly Timer _refreshTimer;

    public async Task AddPlacaAsync(string placa, string cliId)
    {
        var placaNormalizada = VehicleHelper.GetPlacaPrefix(placa);
        var cache = await GetCacheAsync();
        cache[placaNormalizada] = cliId;

        _logger.LogInformation("Placa {Placa} adicionada ao cache para cliente {CliId}", placaNormalizada, cliId);
    }

    public void Dispose()
    {
        _refreshTimer?.Dispose();
    }

    public async Task<string?> GetCliIdByPlacaAsync(string placa)
    {
        var placaNormalizada = VehicleHelper.GetPlacaPrefix(placa);
        var cache = await GetCacheAsync();
        return cache.TryGetValue(placaNormalizada, out var cliId) ? cliId : null;
    }

    public PlacaCacheService(IPlacaRepository placaRepository, IMemoryCache memoryCache, ILogger<PlacaCacheService> logger)
    {
        _placaRepository = placaRepository;
        _memoryCache = memoryCache;
        _logger = logger;

        _refreshTimer = new Timer(async _ => await RefreshCacheAsync(), null, TimeSpan.Zero, TimeSpan.FromMinutes(REFRESH_MINUTES));
    }

    public async Task RemovePlacaAsync(string placa)
    {
        var placaNormalizada = VehicleHelper.GetPlacaPrefix(placa);
        var cache = await GetCacheAsync();
        cache.Remove(placaNormalizada);

        _logger.LogInformation("Placa {Placa} removida do cache", placaNormalizada);
    }
}