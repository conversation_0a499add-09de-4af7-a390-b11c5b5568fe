using System.Text.Json;
using Weso.ProcessTorreKlabin.Dto;

namespace Weso.ProcessTorreKlabin.Business;

public static class GeoUtils
{
    private static double ToRadians(double degrees)
    {
        return degrees * Math.PI / 180;
    }

    private const double EarthRadiusInMeters = 6371000;

    public static double CalculateDistance(double lat1, double lon1, double lat2, double lon2)
    {
        var dLat = ToRadians(lat2 - lat1);
        var dLon = ToRadians(lon2 - lon1);

        var a = Math.Sin(dLat / 2) * Math.Sin(dLat / 2) +
                Math.Cos(ToRadians(lat1)) * Math.Cos(ToRadians(lat2)) *
                Math.Sin(dLon / 2) * Math.Sin(dLon / 2);

        var c = 2 * Math.Atan2(Math.Sqrt(a), Math.Sqrt(1 - a));

        return EarthRadiusInMeters * c;
    }

    public static bool IsPointInsideRadius(double pointLat, double pointLon, double pcoLat, double pcoLon, int radiusInMeters)
    {
        var distance = CalculateDistance(pointLat, pointLon, pcoLat, pcoLon);
        return distance <= radiusInMeters;
    }

    /// <summary>
    /// Verifica se um ponto está dentro de um polígono usando o algoritmo Ray Casting
    /// </summary>
    /// <param name="pointLat">Latitude do ponto</param>
    /// <param name="pointLon">Longitude do ponto</param>
    /// <param name="geoJsonPolygon">String GeoJSON do polígono</param>
    /// <returns>True se o ponto estiver dentro do polígono</returns>
    public static bool IsPointInsidePolygon(double pointLat, double pointLon, string geoJsonPolygon)
    {
        try
        {
            var geoJson = JsonSerializer.Deserialize<JsonElement>(geoJsonPolygon);

            if (!geoJson.TryGetProperty("type", out var typeElement) ||
                typeElement.GetString() != "Polygon")
            {
                return false;
            }

            if (!geoJson.TryGetProperty("coordinates", out var coordinatesElement))
            {
                return false;
            }

            // GeoJSON Polygon tem array de arrays de coordenadas
            // O primeiro array é o anel exterior, os demais são buracos
            var rings = coordinatesElement.EnumerateArray().ToList();

            if (rings.Count == 0)
            {
                return false;
            }

            // Verifica se está dentro do anel exterior
            var exteriorRing = rings[0].EnumerateArray().ToList();
            var isInside = IsPointInRing(pointLat, pointLon, exteriorRing);

            // Se não está dentro do anel exterior, não está no polígono
            if (!isInside)
            {
                return false;
            }

            // Verifica se está dentro de algum buraco (anéis interiores)
            for (int i = 1; i < rings.Count; i++)
            {
                var interiorRing = rings[i].EnumerateArray().ToList();
                if (IsPointInRing(pointLat, pointLon, interiorRing))
                {
                    return false; // Está dentro de um buraco
                }
            }

            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Verifica se um ponto está dentro de um anel de polígono usando Ray Casting
    /// </summary>
    private static bool IsPointInRing(double pointLat, double pointLon, List<JsonElement> ring)
    {
        if (ring.Count < 3)
        {
            return false;
        }

        bool isInside = false;
        int j = ring.Count - 1;

        for (int i = 0; i < ring.Count; i++)
        {
            var coordI = ring[i].EnumerateArray().ToList();
            var coordJ = ring[j].EnumerateArray().ToList();

            if (coordI.Count < 2 || coordJ.Count < 2)
            {
                continue;
            }

            var lonI = coordI[0].GetDouble();
            var latI = coordI[1].GetDouble();
            var lonJ = coordJ[0].GetDouble();
            var latJ = coordJ[1].GetDouble();

            if (((latI > pointLat) != (latJ > pointLat)) &&
                (pointLon < (lonJ - lonI) * (pointLat - latI) / (latJ - latI) + lonI))
            {
                isInside = !isInside;
            }

            j = i;
        }

        return isInside;
    }

    /// <summary>
    /// Verifica se um ponto está dentro de um PCO (círculo ou polígono)
    /// </summary>
    /// <param name="pointLat">Latitude do ponto</param>
    /// <param name="pointLon">Longitude do ponto</param>
    /// <param name="pco">Ponto de controle</param>
    /// <returns>True se o ponto estiver dentro do PCO</returns>
    public static bool IsPointInsidePco(double pointLat, double pointLon, PcoDto pco)
    {
        if (pco.IsPolygon)
        {
            return IsPointInsidePolygon(pointLat, pointLon, pco.GeoJson!);
        }
        else
        {
            return IsPointInsideRadius(pointLat, pointLon, pco.Lat, pco.Lon, pco.Raio);
        }
    }
}