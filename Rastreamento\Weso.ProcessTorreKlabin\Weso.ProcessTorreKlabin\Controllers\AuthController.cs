using Microsoft.AspNetCore.Mvc;
using Weso.ProcessTorreKlabin.Business;
using Weso.ProcessTorreKlabin.Dto;

namespace Weso.ProcessTorreKlabin.Controllers;

[ApiController]
[Route("api/[controller]")]
public class AuthController(AuthService authService) : ControllerBase
{
    private readonly AuthService _authService = authService;

    [HttpPost("login")]
    [ProducesResponseType(typeof(LoginResponseDto), 200)]
    [ProducesResponseType(typeof(string), 401)]
    public async Task<IActionResult> Login([FromBody] LoginDto loginDto)
    {
        var result = await _authService.AuthenticateAsync(loginDto);

        if (result == null)
            return Unauthorized("Credenciais inválidas");

        return Ok(result);
    }
}