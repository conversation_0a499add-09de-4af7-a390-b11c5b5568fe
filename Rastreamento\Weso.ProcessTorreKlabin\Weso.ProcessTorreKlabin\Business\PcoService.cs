using Weso.ProcessTorreKlabin.Dto;
using Weso.ProcessTorreKlabin.Repository;

namespace Weso.ProcessTorreKlabin.Business;

public class PcoService
{
    private readonly ICacheRepository _cacheRepository;

    public async Task<PcoDto> CreateAsync(PcoDto pco)
    {
        if (string.IsNullOrEmpty(pco.Id))
        {
            pco.Id = Guid.NewGuid().ToString();
        }
        _cacheRepository.SetPco(pco.Cli, pco);
        return await Task.FromResult(pco);
    }

    public async Task<bool> DeleteAsync(string id, string cliId)
    {
        var existing = _cacheRepository.GetPco(cliId, id);
        if (existing == null)
            return false;

        return await Task.FromResult(_cacheRepository.DeletePco(cliId, id));
    }

    public async Task<List<PcoDto>> GetAllAsync(string cliId)
    {
        return await Task.FromResult(_cacheRepository.GetPcos(cliId));
    }

    public async Task<PcoDto?> GetByIdAsync(string id, string cliId)
    {
        return await Task.FromResult(_cacheRepository.GetPco(cliId, id));
    }

    public PcoService(ICacheRepository cacheRepository)
    {
        _cacheRepository = cacheRepository;
    }

    public async Task<PcoDto?> UpdateAsync(string id, PcoDto pco, string cliId)
    {
        var existing = _cacheRepository.GetPco(cliId, id);
        if (existing == null)
            return null;

        pco.Id = id;
        pco.Cli = cliId;
        _cacheRepository.SetPco(cliId, pco);
        return await Task.FromResult(pco);
    }
}