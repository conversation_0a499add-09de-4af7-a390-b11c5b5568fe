using MongoDB.Driver;
using Weso.ProcessTorreKlabin.Dto;
using Weso.ProcessTorreKlabin.Utils;

namespace Weso.ProcessTorreKlabin.Repository;

public class MongoPlacaRepository : IPlacaRepository
{
    private readonly IMongoCollection<PlacaDto> _placaCollection;

    public MongoPlacaRepository(IConfiguration configuration)
    {
        var connectionString = configuration.GetConnectionString("MongoDB");
        var client = new MongoClient(connectionString);
        var database = client.GetDatabase("klabin");
        _placaCollection = database.GetCollection<PlacaDto>("placas");

        // Criar índice único na placa para evitar duplicatas
        var indexKeysDefinition = Builders<PlacaDto>.IndexKeys.Ascending(p => p.Placa);
        var indexOptions = new CreateIndexOptions { Unique = true };
        var indexModel = new CreateIndexModel<PlacaDto>(indexKeysDefinition, indexOptions);

        try
        {
            _placaCollection.Indexes.CreateOne(indexModel);
        }
        catch
        {
            // Índice já existe
        }
    }

    public async Task<PlacaDto?> GetByPlacaAsync(string placa)
    {
        var placaNormalizada = VehicleHelper.GetPlacaPrefix(placa);
        return await _placaCollection
            .Find(p => p.Placa == placaNormalizada)
            .FirstOrDefaultAsync();
    }

    public async Task<List<PlacaDto>> GetByCliAsync(string cli)
    {
        return await _placaCollection
            .Find(p => p.Cli == cli)
            .SortBy(p => p.Placa)
            .ToListAsync();
    }

    public async Task<List<PlacaDto>> GetAllPlacasAsync()
    {
        return await _placaCollection
            .Find(FilterDefinition<PlacaDto>.Empty)
            .SortBy(p => p.Placa)
            .ToListAsync();
    }

    public async Task<PlacaDto> CreateAsync(PlacaDto placa)
    {
        placa.Placa = VehicleHelper.GetPlacaPrefix(placa.Placa);
        await _placaCollection.InsertOneAsync(placa);
        return placa;
    }

    public async Task<bool> DeleteByPlacaAsync(string placa)
    {
        var placaNormalizada = VehicleHelper.GetPlacaPrefix(placa);
        var result = await _placaCollection.DeleteOneAsync(p => p.Placa == placaNormalizada);
        return result.DeletedCount > 0;
    }

    public async Task<bool> ExistsByPlacaAsync(string placa)
    {
        var placaNormalizada = VehicleHelper.GetPlacaPrefix(placa);
        var count = await _placaCollection.CountDocumentsAsync(p => p.Placa == placaNormalizada);
        return count > 0;
    }
}