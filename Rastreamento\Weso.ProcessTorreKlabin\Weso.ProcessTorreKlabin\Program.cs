using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using StackExchange.Redis;
using System.Reflection;
using System.Text;
using Weso.ProcessTorreKlabin.Business;
using Weso.ProcessTorreKlabin.Hubs;
using Weso.ProcessTorreKlabin.Repository;
using Weso.ProcessTorreKlabin.Workers;

var builder = WebApplication.CreateBuilder(args);

builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "Klabin API",
        Version = "v1",
        Description = "API do Sistema de Rastreamento Klabin",
        Contact = new OpenApiContact
        {
            Name = "Sistema Klabin",
            Email = "<EMAIL>"
        }
    });

    var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    c.IncludeXmlComments(xmlPath);

    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = "JWT Authorization header usando o esquema Bearer. Exemplo: \"Authorization: Bearer {token}\"",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });

    c.AddSecurityRequirement(new OpenApiSecurityRequirement()
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                },
                Scheme = "oauth2",
                Name = "Bearer",
                In = ParameterLocation.Header,
            },
            new List<string>()
        }
    });
});

builder.Services.AddMemoryCache();

builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowReactApp", policy =>
    {
        policy.WithOrigins("http://localhost:3000", "http://localhost:3237", "https://torre.srv.weso.com.br")
             .AllowAnyHeader()
             .AllowAnyMethod()
             .AllowCredentials();
    });

    options.AddPolicy("AllowAll", policy =>
    {
        policy.WithOrigins("https://torre.srv.weso.com.br", "http://localhost:3000", "http://localhost:3237")
              .AllowAnyHeader()
              .AllowAnyMethod()
              .AllowCredentials();
    });
});

builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = builder.Configuration["Jwt:Issuer"] ?? "klabin",
            ValidAudience = builder.Configuration["Jwt:Audience"] ?? "klabin",
            IssuerSigningKey = new SymmetricSecurityKey(
                Encoding.UTF8.GetBytes(builder.Configuration["Jwt:Key"] ?? "klabin-secret-key-very-long-string-for-security"))
        };

        options.Events = new JwtBearerEvents
        {
            OnMessageReceived = context =>
            {
                var accessToken = context.Request.Query["access_token"];
                var path = context.HttpContext.Request.Path;
                if (!string.IsNullOrEmpty(accessToken) && path.StartsWithSegments("/vehicleHub"))
                {
                    context.Token = accessToken;
                }
                return Task.CompletedTask;
            },
            OnChallenge = context =>
            {
                // Skip the default logic and avoid returning 401 for SignalR
                if (context.Request.Path.StartsWithSegments("/vehicleHub"))
                {
                    context.HandleResponse();
                }
                return Task.CompletedTask;
            }
        };
    });

builder.Services.AddSignalR();

builder.Services.AddSingleton<IConnectionMultiplexer>(provider =>
{
    var connectionString = builder.Configuration.GetConnectionString("Redis");
    return ConnectionMultiplexer.Connect(connectionString);
});

builder.Services.AddSingleton<ICacheRepository, CacheRepository>();
builder.Services.AddSingleton<IPositionRepository, MongoPositionRepository>();
builder.Services.AddScoped<IUserRepository, MongoUserRepository>();
builder.Services.AddSingleton<IPlacaRepository, MongoPlacaRepository>();
builder.Services.AddSingleton<PlacaCacheService>();
builder.Services.AddSingleton<PositionHandler>();
builder.Services.AddScoped<AuthService>();
builder.Services.AddScoped<PcoService>();

builder.Services.AddHostedService<TorreKlabinWorker>();

var app = builder.Build();

if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "Klabin API v1");
        c.RoutePrefix = "swagger";
        c.DocumentTitle = "Klabin API - Documentação";
        c.DefaultModelsExpandDepth(-1);
        c.DocExpansion(Swashbuckle.AspNetCore.SwaggerUI.DocExpansion.None);
        c.EnableDeepLinking();
        c.EnableFilter();
        c.ShowExtensions();
    });
}

if (app.Environment.IsDevelopment())
{
    app.UseCors("AllowReactApp");
}
else
{
    app.UseCors("AllowAll");
}
app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();
app.MapHub<VehicleHub>("/vehicleHub");

app.Run();