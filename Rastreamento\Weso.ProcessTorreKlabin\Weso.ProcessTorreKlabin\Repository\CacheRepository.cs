using Newtonsoft.Json;
using StackExchange.Redis;
using Weso.ProcessTorreKlabin.Dto;
using Weso.ProcessTorreKlabin.Utils;

namespace Weso.ProcessTorreKlabin.Repository;

public class CacheRepository : ICacheRepository, IDisposable
{
    private static string GetEqpPrefix(string integracao)
    {
        return $"{integracao}_{EQP_PREFIX}";
    }

    private static string GetKey(string cli, string prefix)
    {
        return $"torreklabin:{cli}:{prefix}";
    }



    private const string EQP_PREFIX = "eqps";
    private const string PCO_PREFIX = "pcos";
    private readonly IDatabase _database;
    private readonly ConnectionMultiplexer _redis;

    public CacheRepository(IConfiguration configuration)
    {
        var connectionString = configuration.GetConnectionString("Redis");
        _redis = ConnectionMultiplexer.Connect(connectionString);
        _database = _redis.GetDatabase();
    }

    public bool DeletePco(string cli, string id)
    {
        try
        {
            var key = GetKey(cli, PCO_PREFIX);
            return _database.HashDelete(key, id);
        }
        catch
        {
            return false;
        }
    }



    public void Dispose()
    {
        _redis?.Dispose();
    }

    public List<CacheEqpDto> GetAllEquipments(string cli, string integracao)
    {
        try
        {
            var hashEntries = _database.HashGetAll(GetKey(cli, GetEqpPrefix(integracao)));
            var equipments = new List<CacheEqpDto>();

            foreach (var entry in hashEntries)
            {
                var equipment = JsonConvert.DeserializeObject<CacheEqpDto>(entry.Value!);
                if (equipment != null)
                    equipments.Add(equipment);
            }

            return equipments;
        }
        catch
        {
            return new List<CacheEqpDto>();
        }
    }

    public CacheEqpDto? GetCachePlaca(string cli, string placa, string integracao)
    {
        try
        {
            var json = _database.HashGet(GetKey(cli, GetEqpPrefix(integracao)), VehicleHelper.GetPlacaPrefix(placa));
            if (!json.HasValue)
                return null;

            return JsonConvert.DeserializeObject<CacheEqpDto>(json!);
        }
        catch
        {
            return null;
        }
    }



    public PcoDto? GetPco(string cli, string id)
    {
        try
        {
            var json = _database.HashGet(GetKey(cli, PCO_PREFIX), id);
            if (!json.HasValue)
                return null;

            return JsonConvert.DeserializeObject<PcoDto>(json!);
        }
        catch
        {
            return null;
        }
    }

    public List<PcoDto> GetPcos(string cli)
    {
        try
        {
            var hashEntries = _database.HashGetAll(GetKey(cli, PCO_PREFIX));
            var pcos = new List<PcoDto>();

            foreach (var entry in hashEntries)
            {
                var pco = JsonConvert.DeserializeObject<PcoDto>(entry.Value!);
                if (pco != null)
                    pcos.Add(pco);
            }

            return pcos;
        }
        catch
        {
            return new List<PcoDto>();
        }
    }

    public void SetCacheEqp(string cli, string placa, CacheEqpDto cacheEqp, string integracao)
    {
        var json = JsonConvert.SerializeObject(cacheEqp);
        _database.HashSet(GetKey(cli, GetEqpPrefix(integracao)), VehicleHelper.GetPlacaPrefix(placa), json);
    }

    public void SetPco(string cli, PcoDto pco)
    {
        var key = GetKey(cli, PCO_PREFIX);
        var json = JsonConvert.SerializeObject(pco);
        _database.HashSet(key, pco.Id, json);
    }

    public void SetPcos(string cli, List<PcoDto> pcos)
    {
        var key = GetKey(cli, PCO_PREFIX);

        foreach (var pco in pcos)
        {
            var json = JsonConvert.SerializeObject(pco);
            _database.HashSet(key, pco.Id, json);
        }
    }
}