using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Caching.Memory;
using Weso.ProcessTorreKlabin.Dto;
using Weso.ProcessTorreKlabin.Hubs;
using Weso.ProcessTorreKlabin.Repository;

namespace Weso.ProcessTorreKlabin.Business;

public class PositionHandler(ICacheRepository cacheRepository, PlacaCacheService placaCacheService, IMemoryCache memoryCache, IHubContext<VehicleHub> hubContext)
{
    private static string CalculatePcoStatus(CacheEqpDto? previousCache, PcoDto? currentPco)
    {
        var wasPreviouslyInPco = previousCache != null && !string.IsNullOrEmpty(previousCache.PcoId);
        var isCurrentlyInPco = currentPco != null;

        if (!wasPreviouslyInPco && isCurrentlyInPco)
        {
            return "E";
        }
        else if (wasPreviouslyInPco && !isCurrentlyInPco)
        {
            return "S";
        }
        else if (wasPreviouslyInPco && isCurrentlyInPco)
        {
            return "D";
        }

        return previousCache?.PcoStatus;
    }

    private static (EquipmentStatus status, bool finalizouViagem) CalculateStatus(CacheEqpDto? previousCache, PcoDto? currentPco, string currentPcoStatus, PositionDto position)
    {
        if (currentPco != null && !position.Ign)
        {
            if (previousCache != null && !string.IsNullOrEmpty(previousCache.OrigemPcoId) && currentPco.Id == previousCache.OrigemPcoId && currentPco.Tipo == "C")
            {
                return (EquipmentStatus.Carga, false);
            }

            if (previousCache != null && !string.IsNullOrEmpty(previousCache.DestinoPcoId) && currentPco.Id == previousCache.DestinoPcoId && currentPco.Tipo == "D")
            {
                return (EquipmentStatus.Descarga, false);
            }

            if (currentPco.Tipo == "C" && !position.Ign)
                return (EquipmentStatus.Carga, false);

            if (currentPco.Tipo == "D" && !position.Ign)
                return (EquipmentStatus.Descarga, false);
        }

        if (currentPco == null && previousCache != null)
        {
            if (previousCache.Status == EquipmentStatus.Carga && currentPcoStatus.Equals("S"))
                return (EquipmentStatus.EmViagem, false);

            if (previousCache.Status == EquipmentStatus.Descarga && currentPcoStatus.Equals("S"))
                return (EquipmentStatus.Descanso, true);
        }

        if (previousCache == null)
            return (EquipmentStatus.Desconhecido, false);

        return (previousCache.Status, false);
    }

    private static long CalculateTempoDesl(CacheEqpDto? previousCache, PositionDto currentPosition, EquipmentStatus currentStatus)
    {
        if (previousCache == null)
            return 0;

        if (currentStatus != EquipmentStatus.Descanso)
            return 0;

        if (previousCache.Status != EquipmentStatus.Descanso)
            return 0;

        if (currentPosition.Ign)
            return previousCache.TempoDesl;

        var timeDifference = (currentPosition.Dt - previousCache.PosDate).TotalSeconds;

        if (timeDifference > 0)
            return previousCache.TempoDesl + (long)timeDifference;

        return previousCache.TempoDesl;
    }

    private static void ClearRouteData(CacheEqpDto cacheEqp)
    {
        cacheEqp.OrigemPcoId = "";
        cacheEqp.DestinoPcoId = "";
        cacheEqp.DataLimite = null;
    }

    private static PcoDto? GetPcoForPosition(PositionDto position, List<PcoDto> pcos)
    {
        return pcos.FirstOrDefault(pco =>
            GeoUtils.IsPointInsidePco(position.Lat, position.Lon, pco));
    }

    private static bool ShouldClearRouteData(CacheEqpDto? previousCache, EquipmentStatus newStatus)
    {
        if (previousCache == null)
            return false;

        return newStatus == EquipmentStatus.Descanso &&
               previousCache.Status == EquipmentStatus.Descarga;
    }

    private async Task ProcessPosition(PositionDto position, List<PcoDto> pcos, string integracao, string cli)
    {
        var cacheEqp = _cacheRepository.GetCachePlaca(cli, position.Placa, integracao);
        var currentPco = GetPcoForPosition(position, pcos);
        var currentPcoStatus = CalculatePcoStatus(cacheEqp, currentPco);
        var (newStatus, finalizouViagem) = CalculateStatus(cacheEqp, currentPco, currentPcoStatus, position);

        var newCacheEqp = new CacheEqpDto
        {
            Eqp = position.Eqp,
            Placa = position.Placa,
            Date = DateTime.Now,
            PosDate = position.Dt,
            Cli = cli,
            Lat = position.Lat,
            Lon = position.Lon,
            Status = newStatus,
            PcoStatus = currentPcoStatus,
            PcoType = currentPco?.Tipo ?? (cacheEqp?.PcoType ?? ""),
            PcoName = currentPco?.Nome ?? (cacheEqp?.PcoName ?? ""),
            PcoId = currentPco?.Id ?? (cacheEqp?.PcoId ?? ""),
            TempoDesl = CalculateTempoDesl(cacheEqp, position, newStatus),
            Apto = 0,
            OrigemPcoId = cacheEqp?.OrigemPcoId ?? "",
            DestinoPcoId = cacheEqp?.DestinoPcoId ?? "",
            DataLimite = cacheEqp?.DataLimite
        };

        if (finalizouViagem)
        {
            newCacheEqp.OrigemPcoId = string.Empty;
            newCacheEqp.DestinoPcoId = .Empty;
            newCacheEqp.DataLimite = null;
        }

        newCacheEqp.Apto = newCacheEqp.TempoDesl >= ELEVEN_HOURS_IN_SECONDS ? 1 : 0;

        if (ShouldClearRouteData(cacheEqp, newStatus))
        {
            ClearRouteData(newCacheEqp);
        }

        _cacheRepository.SetCacheEqp(cli, position.Placa, newCacheEqp, integracao);

        await _hubContext.Clients.Group($"cli_{cli}").SendAsync("VehicleUpdate", newCacheEqp);
    }

    private const int CACHE_MINUTES_EXP = 10;
    private const long ELEVEN_HOURS_IN_SECONDS = 11 * 60 * 60;
    private readonly ICacheRepository _cacheRepository = cacheRepository;
    private readonly IHubContext<VehicleHub> _hubContext = hubContext;
    private readonly IMemoryCache _memoryCache = memoryCache;
    private readonly PlacaCacheService _placaCacheService = placaCacheService;

    public List<PcoDto> GetPcos(string cli)
    {
        var cacheKey = "pcos_" + cli;

        if (_memoryCache.TryGetValue(cacheKey, out List<PcoDto>? cachedPcos) && cachedPcos != null)
        {
            return cachedPcos;
        }

        var pcos = _cacheRepository.GetPcos(cli);

        _memoryCache.Set(cacheKey, pcos, TimeSpan.FromMinutes(CACHE_MINUTES_EXP));

        return pcos;
    }

    public async Task Process(List<PositionDto> positions, string integracao)
    {
        var positionsGroupEqp = positions.GroupBy(x => x.Placa).ToList();

        foreach (var eqpPos in positionsGroupEqp)
        {
            var placa = eqpPos.Key;
            var cli = await _placaCacheService.GetCliIdByPlacaAsync(placa);

            if (!string.IsNullOrEmpty(cli))
            {
                var pcos = GetPcos(cli);
                foreach (var pos in eqpPos.OrderBy(x => x.Dt))
                {
                    await ProcessPosition(pos, pcos, integracao, cli);
                }
            }
        }
    }
}