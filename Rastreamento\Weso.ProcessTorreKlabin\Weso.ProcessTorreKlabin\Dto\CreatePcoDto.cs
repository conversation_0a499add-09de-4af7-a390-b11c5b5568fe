using System.ComponentModel.DataAnnotations;

namespace Weso.ProcessTorreKlabin.Dto;

/// <summary>
/// DTO para criação de Ponto de Controle (PCO)
/// </summary>
public class CreatePcoDto
{
    /// <summary>
    /// Descrição do ponto de controle
    /// </summary>
    /// <example>Ponto de carregamento principal</example>
    [Required(ErrorMessage = "Descrição é obrigatória")]
    public string Desc { get; set; } = string.Empty;

    /// <summary>
    /// Latitude da localização (para pontos circulares)
    /// </summary>
    /// <example>-23.550520</example>
    [Range(-90, 90, ErrorMessage = "Latitude deve estar entre -90 e 90")]
    public double Lat { get; set; }

    /// <summary>
    /// Longitude da localização (para pontos circulares)
    /// </summary>
    /// <example>-46.633308</example>
    [Range(-180, 180, ErrorMessage = "Longitude deve estar entre -180 e 180")]
    public double Lon { get; set; }

    /// <summary>
    /// Nome do ponto de controle
    /// </summary>
    /// <example>PCO São Paulo Centro</example>
    [Required(ErrorMessage = "Nome é obrigatório")]
    public string Nome { get; set; } = string.Empty;

    /// <summary>
    /// Raio de controle em metros (para pontos circulares)
    /// </summary>
    /// <example>100</example>
    [Range(10, 10000, ErrorMessage = "Raio deve estar entre 10 e 10000 metros")]
    public int Raio { get; set; }

    /// <summary>
    /// Tipo do ponto de controle
    /// </summary>
    /// <example>carga</example>
    [Required(ErrorMessage = "Tipo é obrigatório")]
    public string Tipo { get; set; } = string.Empty;

    /// <summary>
    /// Geometria do ponto de controle em formato GeoJSON
    /// Para pontos circulares, será null e utilizará Lat/Lon/Raio
    /// Para polígonos, conterá a geometria do polígono
    /// </summary>
    /// <example>{"type":"Polygon","coordinates":[[[-46.633308,-23.550520],[-46.633208,-23.550520],[-46.633208,-23.550420],[-46.633308,-23.550420],[-46.633308,-23.550520]]]}</example>
    public string? GeoJson { get; set; }

    /// <summary>
    /// Tipo de geometria: "Circle" para pontos circulares, "Polygon" para polígonos
    /// </summary>
    /// <example>Circle</example>
    public string GeometryType { get; set; } = "Circle";

    /// <summary>
    /// Verifica se o PCO é do tipo polígono
    /// </summary>
    public bool IsPolygon => GeometryType == "Polygon" && !string.IsNullOrEmpty(GeoJson);

    /// <summary>
    /// Verifica se o PCO é do tipo circular
    /// </summary>
    public bool IsCircle => GeometryType == "Circle" || string.IsNullOrEmpty(GeoJson);
}