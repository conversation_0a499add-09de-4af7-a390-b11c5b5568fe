using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Text.Json;
using Weso.ProcessTorreKlabin.Business;
using Weso.ProcessTorreKlabin.Dto;

namespace Weso.ProcessTorreKlabin.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class PcoController : ControllerBase
{
    private readonly PcoService _pcoService;

    [HttpPost]
    [ProducesResponseType(typeof(PcoDto), 201)]
    [ProducesResponseType(400)]
    [ProducesResponseType(401)]
    public async Task<IActionResult> Create([FromBody] CreatePcoDto createPco)
    {
        var cliId = User.FindFirst("CliId")?.Value;

        // Validações específicas para cada tipo de geometria
        if (createPco.IsPolygon)
        {
            if (string.IsNullOrEmpty(createPco.GeoJson))
            {
                return BadRequest("GeoJSON é obrigatório para polígonos");
            }

            // Validar se o GeoJSON é válido
            if (!IsValidGeoJsonPolygon(createPco.GeoJson))
            {
                return BadRequest("GeoJSON inválido para polígono");
            }
        }
        else
        {
            // Para círculos, validar se lat/lon/raio estão definidos
            if (createPco.Lat == 0 && createPco.Lon == 0)
            {
                return BadRequest("Latitude e longitude são obrigatórias para pontos circulares");
            }

            if (createPco.Raio <= 0)
            {
                return BadRequest("Raio deve ser maior que zero para pontos circulares");
            }
        }

        var pco = new PcoDto
        {
            Desc = createPco.Desc,
            Lat = createPco.Lat,
            Lon = createPco.Lon,
            Nome = createPco.Nome,
            Raio = createPco.Raio,
            Tipo = createPco.Tipo,
            GeoJson = createPco.GeoJson,
            GeometryType = createPco.GeometryType,
            Cli = cliId,
            Id = null
        };

        var created = await _pcoService.CreateAsync(pco);
        return CreatedAtAction(nameof(GetById), new { id = created.Id }, created);
    }

    [HttpDelete("{id}")]
    [ProducesResponseType(204)]
    [ProducesResponseType(404)]
    [ProducesResponseType(401)]
    public async Task<IActionResult> Delete(string id)
    {
        var cliId = User.FindFirst("CliId")?.Value;
        var deleted = await _pcoService.DeleteAsync(id, cliId);

        if (!deleted)
            return NotFound();

        return NoContent();
    }

    [HttpGet]
    [ProducesResponseType(typeof(List<PcoDto>), 200)]
    [ProducesResponseType(401)]
    public async Task<IActionResult> GetAll()
    {
        var cliId = User.FindFirst("CliId")?.Value;
        var pcos = await _pcoService.GetAllAsync(cliId);
        return Ok(pcos);
    }

    [HttpGet("{id}")]
    [ProducesResponseType(typeof(PcoDto), 200)]
    [ProducesResponseType(404)]
    [ProducesResponseType(401)]
    public async Task<IActionResult> GetById(string id)
    {
        var cliId = User.FindFirst("CliId")?.Value;
        var pco = await _pcoService.GetByIdAsync(id, cliId);

        if (pco == null)
            return NotFound();

        return Ok(pco);
    }

    public PcoController(PcoService pcoService)
    {
        _pcoService = pcoService;
    }

    private static bool IsValidGeoJsonPolygon(string geoJson)
    {
        try
        {
            var json = JsonSerializer.Deserialize<JsonElement>(geoJson);

            if (!json.TryGetProperty("type", out var typeElement) ||
                typeElement.GetString() != "Polygon")
            {
                return false;
            }

            if (!json.TryGetProperty("coordinates", out var coordinatesElement))
            {
                return false;
            }

            var rings = coordinatesElement.EnumerateArray().ToList();
            if (rings.Count == 0)
            {
                return false;
            }

            // Validar o anel exterior
            var exteriorRing = rings[0].EnumerateArray().ToList();
            if (exteriorRing.Count < 4)
            {
                return false; // Polígono deve ter pelo menos 4 pontos (fechado)
            }

            // Verificar se o primeiro e último pontos são iguais (polígono fechado)
            var firstPoint = exteriorRing[0].EnumerateArray().ToList();
            var lastPoint = exteriorRing[exteriorRing.Count - 1].EnumerateArray().ToList();

            if (firstPoint.Count < 2 || lastPoint.Count < 2)
            {
                return false;
            }

            if (Math.Abs(firstPoint[0].GetDouble() - lastPoint[0].GetDouble()) > 0.000001 ||
                Math.Abs(firstPoint[1].GetDouble() - lastPoint[1].GetDouble()) > 0.000001)
            {
                return false; // Polígono não está fechado
            }

            return true;
        }
        catch
        {
            return false;
        }
    }

    [HttpPut("{id}")]
    [ProducesResponseType(typeof(PcoDto), 200)]
    [ProducesResponseType(404)]
    [ProducesResponseType(401)]
    public async Task<IActionResult> Update(string id, [FromBody] PcoDto pco)
    {
        var cliId = User.FindFirst("CliId")?.Value;

        // Validações específicas para cada tipo de geometria
        if (pco.IsPolygon)
        {
            if (string.IsNullOrEmpty(pco.GeoJson))
            {
                return BadRequest("GeoJSON é obrigatório para polígonos");
            }

            // Validar se o GeoJSON é válido
            if (!IsValidGeoJsonPolygon(pco.GeoJson))
            {
                return BadRequest("GeoJSON inválido para polígono");
            }
        }
        else
        {
            // Para círculos, validar se lat/lon/raio estão definidos
            if (pco.Lat == 0 && pco.Lon == 0)
            {
                return BadRequest("Latitude e longitude são obrigatórias para pontos circulares");
            }

            if (pco.Raio <= 0)
            {
                return BadRequest("Raio deve ser maior que zero para pontos circulares");
            }
        }

        var updated = await _pcoService.UpdateAsync(id, pco, cliId);

        if (updated == null)
            return NotFound();

        return Ok(updated);
    }
}