using MongoDB.Driver;
using Weso.ProcessTorreKlabin.Dto;

namespace Weso.ProcessTorreKlabin.Repository;

public class MongoUserRepository : IUserRepository
{
    private readonly IMongoCollection<UserDto> _users;

    public async Task CreateUserAsync(UserDto user)
    {
        await _users.InsertOneAsync(user);
    }

    public async Task<UserDto> GetByLoginAsync(string login)
    {
        return await _users.Find(u => u.Login == login).FirstOrDefaultAsync();
    }

    public MongoUserRepository(IConfiguration configuration)
    {
        var connectionString = configuration.GetConnectionString("MongoDB");
        var client = new MongoClient(connectionString);
        var database = client.GetDatabase("klabin");
        _users = database.GetCollection<UserDto>("usuarios");
    }
}