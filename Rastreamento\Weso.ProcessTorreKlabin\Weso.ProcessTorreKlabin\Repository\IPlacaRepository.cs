using Weso.ProcessTorreKlabin.Dto;

namespace Weso.ProcessTorreKlabin.Repository;

public interface IPlacaRepository
{
    Task<PlacaDto?> GetByPlacaAsync(string placa);
    Task<List<PlacaDto>> GetByCliAsync(string cli);
    Task<List<PlacaDto>> GetAllPlacasAsync();
    Task<PlacaDto> CreateAsync(PlacaDto placa);
    Task<bool> DeleteByPlacaAsync(string placa);
    Task<bool> ExistsByPlacaAsync(string placa);
}