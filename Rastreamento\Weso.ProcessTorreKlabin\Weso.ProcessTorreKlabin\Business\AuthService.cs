using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using Microsoft.IdentityModel.Tokens;
using Weso.ProcessTorreKlabin.Dto;
using Weso.ProcessTorreKlabin.Repository;

namespace Weso.ProcessTorreKlabin.Business;

public class AuthService
{
    private readonly IUserRepository _userRepository;
    private readonly IConfiguration _configuration;

    public AuthService(IUserRepository userRepository, IConfiguration configuration)
    {
        _userRepository = userRepository;
        _configuration = configuration;
    }

    public async Task<LoginResponseDto> AuthenticateAsync(LoginDto loginDto)
    {
        var user = await _userRepository.GetByLoginAsync(loginDto.Login);

        if (user == null || !BCrypt.Net.BCrypt.Verify(loginDto.Senha, user.Senha))
            return null;

        var token = GenerateJwtToken(user);

        return new LoginResponseDto
        {
            Token = token,
            CliId = user.CliId,
            Login = user.Login
        };
    }

    private string GenerateJwtToken(UserDto user)
    {
        var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_configuration["Jwt:Key"] ?? "klabin-secret-key-very-long-string-for-security"));
        var credentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

        var claims = new[]
        {
            new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
            new Claim(ClaimTypes.Name, user.Login),
            new Claim("CliId", user.CliId.ToString())
        };

        var token = new JwtSecurityToken(
            issuer: _configuration["Jwt:Issuer"] ?? "klabin",
            audience: _configuration["Jwt:Audience"] ?? "klabin",
            claims: claims,
            expires: DateTime.Now.AddHours(24),
            signingCredentials: credentials
        );

        return new JwtSecurityTokenHandler().WriteToken(token);
    }
}